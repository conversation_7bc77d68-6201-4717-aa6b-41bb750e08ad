# 🎮 GamePort - 静态游戏门户网站模板

一个高性能、SEO友好、易于维护的静态游戏门户网站模板。支持多主题切换、响应式设计、无需数据库的纯静态部署。

## ✨ 特性

- 🚀 **高性能**: 纯静态HTML，加载速度极快
- 🎨 **多主题支持**: 6种内置主题（默认、复古、赛博朋克、森林、海洋、战斗）
- 📱 **响应式设计**: 完美支持手机、平板、桌面设备
- 🔍 **SEO优化**: 每个游戏页面独立SEO元数据
- 🛠️ **易于维护**: 通过JSON配置文件管理所有内容
- 🎯 **零依赖**: 无需数据库，纯静态部署
- 🔧 **灵活扩展**: 支持YouTube视频、自定义内容块

## 📁 项目结构

```
GamePort/
├── config.json              # 全局配置文件
├── games.json               # 游戏数据文件
├── build.js                 # 构建脚本
├── package.json             # 项目配置
├── src/                     # 源文件目录
│   ├── templates/           # HTML模板
│   │   ├── _main-layout-template.html      # 主布局模板
│   │   ├── game-detail-template.html       # 游戏详情页模板
│   │   └── _list-layout-template.html      # 列表页模板
│   ├── components/          # 组件模板
│   │   ├── game-card.html   # 游戏卡片组件
│   │   └── feature-blocks.html # 功能块组件
│   ├── css/                 # 样式文件
│   │   ├── style.css        # 主样式文件
│   │   └── themes/          # 主题样式
│   ├── js/                  # JavaScript文件
│   ├── images/              # 图片资源目录
│   │   ├── common/          # 公共图片（Logo、Favicon、首页特殊游戏图片）
│   │   │   ├── logo.png     # 网站Logo（显示在导航栏）
│   │   │   ├── favicon.ico  # 网站图标（浏览器标签页图标）
│   │   │   └── homepage-game.jpeg # 首页特殊游戏缩略图
│   │   ├── popular_games_image/  # 热门游戏缩略图目录
│   │   │   ├── super-mario-bros.png  # 超级马里奥兄弟缩略图
│   │   │   ├── pac-man.png          # 吃豆人缩略图
│   │   │   └── tetris.png           # 俄罗斯方块缩略图
│   │   └── new_games_image/     # 新游戏缩略图目录
│   │       ├── space-invaders.png   # 太空入侵者缩略图
│   │       ├── frogger.png          # 青蛙过河缩略图
│   │       └── snake.png            # 贪吃蛇缩略图
│   └── legal_info/          # 法律信息页面
└── dist/                    # 构建输出目录
```

## 🚀 快速开始

### 环境要求

- Node.js >= 14.0.0
- 无需其他依赖

### 安装和使用

1. **克隆或下载模板**
```bash
# 下载模板到本地
git clone <repository-url> my-game-site
cd my-game-site
```

2. **配置网站信息**

编辑 `config.json` 文件，这是网站的全局配置文件：

```json
{
  // 网站基本信息
  "site_name": "GamePort",                     // 网站名称，显示在页面标题、导航栏、FAQ等位置
  "site_url": "https://gameport.example.com",  // 你的网站域名，用于SEO和链接生成

  // 主题配置 - 支持7种内置主题
  "available_themes": [                        // 可用的主题列表，包含所有内置主题
    "default",    // 默认蓝色主题，简洁现代
    "retro",      // 复古风格，怀旧色彩
    "cyberpunk",  // 赛博朋克风格，霓虹色彩
    "forest",     // 森林主题，绿色自然
    "ocean",      // 海洋主题，蓝色清新
    "battle",     // 战斗主题，红色激烈
    "dark"        // 暗黑主题，深色护眼
  ],
  "selected_theme": "battle",                  // 当前使用的主题，从上面列表中选择

  // 构建设置（一般不需要修改）
  "build_settings": {
    "output_dir": "dist",                      // 构建输出目录
    "copy_assets": true,                       // 是否复制静态资源（图片、CSS、JS等）
    "minify_css": false,                       // 是否压缩CSS文件（生产环境建议true）
    "minify_js": false                         // 是否压缩JavaScript文件（生产环境建议true）
  },

  // SEO优化设置 - 影响搜索引擎排名和社交分享
  "seo": {
    "default_title": "GamePort - Play Free Online Games",           // 默认页面标题，显示在浏览器标签页
    "default_description": "Play the best free online games at GamePort. Enjoy classic arcade games, puzzle games, action games and more!", // 默认页面描述，显示在搜索结果
    "keywords": "online games, free games, arcade games, puzzle games, action games, browser games", // 网站关键词，用逗号分隔，影响SEO
    "author": "GamePort Team",                 // 网站作者或团队名称
    "language": "en",                          // 网站语言（zh-CN中文，en英文）
    "canonical_base_url": "https://gameport.example.com" // 规范化URL，与site_url保持一致，避免重复内容
  },

  // 联系信息配置
  "contact": {
    "email": "<EMAIL>",      // 联系邮箱，显示在联系页面和FAQ
    "response_time": "24-48 hours"             // 预期回复时间，显示给用户
  },

  // 游戏iframe显示设置 - 控制游戏窗口大小
  "iframe_settings": {
    "default_size": "small",                   // 默认游戏窗口大小（small或large）
    "sizes": {
      "small": {                               // 小尺寸设置 - 适合大多数游戏
        "width": 854,                          // 游戏窗口宽度（像素）
        "height": 480,                         // 游戏窗口高度（像素）
        "container_width": "1294px",           // 容器宽度，包含游戏和侧边栏
        "description": "Standard size for most games" // 尺寸描述
      },
      "large": {                               // 大尺寸设置 - 适合需要更大显示区域的游戏
        "width": 960,                          // 游戏窗口宽度（像素）
        "height": 540,                         // 游戏窗口高度（像素）
        "container_width": "1400px",           // 容器宽度
        "description": "Large size for games requiring bigger display area" // 尺寸描述
      }
    }
  },

  // 首页显示设置 - 控制首页展示的游戏内容
  "display_settings": {
    "homepage": {
      "new_games_display": {                   // "新游戏"区域配置
        "count": 8,                            // 显示的游戏数量
        "selection_method": "configured",       // 选择方式（固定为configured）
        "selected_games": [                    // 要显示的游戏ID列表，必须在games.json中存在
          "space-invaders",    // 太空入侵者
          "frogger",          // 青蛙过河
          "snake",            // 贪吃蛇
          "super-mario-bros", // 超级马里奥兄弟
          "pac-man",          // 吃豆人
          "tetris",           // 俄罗斯方块
          "homepage-special", // 首页特殊游戏
          "space-invaders"    // 可以重复显示同一游戏
        ]
      },
      "popular_games_display": {               // "热门游戏"区域配置
        "count": 6,                            // 显示的游戏数量
        "columns": 2,                          // 显示列数（2列布局，最大2列）
        "selection_method": "configured",       // 选择方式（固定为configured）
        "selected_games": [                    // 要显示的游戏ID列表
          "super-mario-bros", // 超级马里奥兄弟
          "pac-man",          // 吃豆人
          "tetris",           // 俄罗斯方块
          "space-invaders",   // 太空入侵者
          "frogger",          // 青蛙过河
          "snake"             // 贪吃蛇
        ]
      }
    }
  }
}
```

**重要配置项说明：**
- `site_name`: 会显示在网站标题、导航栏、FAQ等所有位置，建议使用简短有意义的名称
- `site_url`: 必须是你的实际域名，影响SEO和分享链接，确保以https://开头
- `selected_theme`: 从7个主题中选择一个（default、retro、cyberpunk、forest、ocean、battle、dark）
- `seo.language`: 中文网站用"zh-CN"，英文网站用"en"，影响搜索引擎索引
- `contact.email`: 用户联系邮箱，显示在联系表单和FAQ页面
- `iframe_settings`: 控制游戏窗口大小，small适合大多数游戏，large适合需要更大显示区域的游戏
- `display_settings`: 控制首页显示哪些游戏，游戏ID必须在games.json中存在，可以重复使用同一游戏ID

3. **添加游戏数据**

编辑 `games.json` 文件，这是所有游戏内容的数据源：

**重要：数组第一个对象是首页特殊游戏，从第二个对象开始是普通游戏列表**

```json
[
  {
    // 首页特殊游戏（数组第一个对象，必须存在且ID必须为homepage-special）
    "id": "homepage-special",                  // 固定ID，不能修改，用于首页大图展示
    "name": "Space Odyssey",                   // 游戏显示名称，显示在首页大图区域
    "category": "popular",                     // 游戏分类（popular或new）
    "thumbnail": "images/common/homepage-game.jpeg", // 游戏缩略图路径，相对于项目根目录
    "gameUrl": "https://wowtbc.net/sprunkin/deluxe+ocs/index.html", // 实际游戏链接，可以是外部链接或本地路径

    // 游戏详情页的功能块（可选，用于丰富游戏详情页内容）
    "feature_blocks": [
      {
        // YouTube视频块 - 嵌入游戏演示视频
        "type": "youtube",
        "videoId": "some-video-id",           // YouTube视频ID（从URL中提取，如：https://www.youtube.com/watch?v=some-video-id）
        "title": "Space Odyssey Gameplay"    // 视频标题，显示在视频上方
      },
      {
        // 文本内容块 - 段落格式，用于游戏介绍
        "type": "section",
        "title": "What is Space Odyssey",     // 内容块标题
        "content": "Space Odyssey is an epic space exploration game that takes you on an incredible journey through the stars|Pilot your ship, battle alien fleets, and explore unknown galaxies in this immersive space adventure", // 内容文本，用|分隔多个段落
        "format": "paragraph"                 // 格式：paragraph（段落）
      },
      {
        // 文本内容块 - 列表格式，用于游戏玩法说明
        "type": "section",
        "title": "How to Play",
        "content": "Use the arrow keys or WASD to control your spaceship|Press the spacebar to fire at enemies and use the mouse to navigate menus|Collect resources, upgrade your ship, and make strategic choices|Determine your path through the galaxy", // 用|分隔的列表项
        "format": "list"                      // 格式：list（有序列表）
      }
    ],

    // SEO元数据（可选，覆盖config.json中的默认SEO设置）
    "meta": {
      "title": "Welcome to GamePort | Play the Latest and Hottest Web Games", // 覆盖默认SEO标题
      "description": "GamePort is your ultimate gaming portal, explore massive exciting games." // 覆盖默认SEO描述
    }
  },

  {
    // 普通游戏示例1 - 超级马里奥兄弟
    "id": "super-mario-bros",                 // 游戏唯一ID，用于URL和内部引用
    "name": "Super Mario Bros",               // 游戏名称，显示在卡片和详情页
    "category": "popular",                    // 分类：popular（热门游戏）或new（新游戏）
    "thumbnail": "images/popular_games_image/super-mario-bros.png", // 缩略图路径
    "gameUrl": "https://html5.gamemonetize.co/qotzmhhui1arsut4sg9zuhy7ualnvag4/", // 游戏链接

    // 游戏详情页功能块（可选）
    "feature_blocks": [
      {
        "type": "youtube",
        "videoId": "rLl9XBg7wSs",            // 真实的YouTube视频ID
        "title": "Super Mario Bros Gameplay"
      },
      {
        "type": "section",
        "title": "What is Super Mario Bros",
        "content": "Super Mario Bros is a legendary platform adventure game that revolutionized the gaming industry. It follows Mario on his quest to rescue Princess Peach from the evil Bowser.",
        "format": "paragraph"
      },
      {
        "type": "section",
        "title": "How to Play",
        "content": "Use the arrow keys or WASD to move Mario left and right|Press the spacebar or up arrow to jump|Your goal is to reach the flagpole at the end of each level|Collect coins and power-ups along the way",
        "format": "list"
      }
    ],

    // SEO元数据（可选）
    "meta": {
      "title": "Play Super Mario Bros Online | GamePort",
      "description": "Relive the classic adventure! Play the original Super Mario Bros online for free."
    }
  }
]
```

**feature_blocks详细说明：**

1. **YouTube视频块**：
   ```json
   {
     "type": "youtube",
     "videoId": "dQw4w9WgXcQ",    // 从YouTube URL提取：https://www.youtube.com/watch?v=dQw4w9WgXcQ
     "title": "Video Title"
   }
   ```

2. **文本内容块 - 段落格式**：
   ```json
   {
     "type": "section",
     "title": "Section Title",
     "content": "This is paragraph text that will be displayed as normal paragraph.",
     "format": "paragraph"        // 显示为段落文本
   }
   ```

3. **文本内容块 - 列表格式**：
   ```json
   {
     "type": "section",
     "title": "Section Title",
     "content": "First item|Second item|Third item|Fourth item",  // 必须用|符号分隔每个列表项
     "format": "list"             // 显示为有序列表
   }
   ```

**重要注意事项：**
- 游戏ID必须唯一，不能重复，建议使用小写字母和连字符
- 缩略图路径要确保文件存在，支持jpg、png、gif等格式，建议统一使用300x200px尺寸
- category只能是"popular"（热门游戏）或"new"（新游戏），决定缩略图存放目录
- format只能是"paragraph"（段落）或"list"（列表）
- 列表格式的content必须用"|"分隔每个项目，会自动生成有序列表
- 段落格式的content用"|"分隔多个段落，每个段落单独显示
- 首页显示的游戏ID必须在config.json的display_settings中配置
- gameUrl可以是外部链接（https://）或本地路径（/games-source/）
- YouTube视频ID从完整URL中提取，如：https://www.youtube.com/watch?v=rLl9XBg7wSs 中的 rLl9XBg7wSs
- 首页特殊游戏（homepage-special）的ID不能修改，它有特殊的显示位置和样式
- 游戏缩略图必须放在对应的目录：popular游戏放在popular_games_image/，new游戏放在new_games_image/

4. **准备图片资源**

网站需要以下图片文件，请确保它们存在于正确的目录中：

**必需的图片文件：**
```
src/images/common/
├── logo.png          # 网站Logo（建议尺寸：200x50px，PNG格式，透明背景）
├── favicon.ico       # 网站图标（16x16px或32x32px，ICO格式）
└── homepage-game.jpeg # 首页特殊游戏图片（建议尺寸：800x450px，JPEG格式）

src/images/popular_games_image/
├── super-mario-bros.png  # 超级马里奥兄弟缩略图（建议尺寸：300x200px）
├── pac-man.png          # 吃豆人缩略图（建议尺寸：300x200px）
└── tetris.png           # 俄罗斯方块缩略图（建议尺寸：300x200px）

src/images/new_games_image/
├── space-invaders.png   # 太空入侵者缩略图（建议尺寸：300x200px）
├── frogger.png          # 青蛙过河缩略图（建议尺寸：300x200px）
└── snake.png            # 贪吃蛇缩略图（建议尺寸：300x200px）
```

**图片要求说明：**
- **Logo**: 显示在所有页面的导航栏左上角，建议使用透明背景的PNG格式
- **Favicon**: 显示在浏览器标签页，必须是ICO格式，16x16px或32x32px
- **首页特殊游戏图片**: 显示在首页大图区域，建议使用高质量的JPEG格式
- **游戏缩略图**: 显示在游戏卡片中，建议统一尺寸300x200px，PNG或JPEG格式

5. **构建网站**
```bash
# 构建静态网站
npm run build

# 或者使用开发模式（构建后显示提示）
npm run dev
```

6. **部署网站**
将项目推送到Git仓库，然后连接到Cloudflare Pages或Vercel进行自动部署。详见[部署章节](#📦-部署)。

## 📝 配置说明

### config.json 配置详解

| 字段 | 说明 | 示例值 | 注意事项 |
|------|------|--------|----------|
| `site_name` | 网站名称 | "GamePort" | 显示在所有页面的标题和导航栏 |
| `site_url` | 网站URL | "https://gameport.example.com" | 必须是完整的域名，影响SEO |
| `selected_theme` | 当前主题 | "battle" | 从7个可用主题中选择 |
| `available_themes` | 可用主题列表 | ["default", "retro", "cyberpunk", "forest", "ocean", "battle", "dark"] | 包含所有内置主题 |
| `seo.default_title` | 默认SEO标题 | "GamePort - Play Free Online Games" | 显示在浏览器标签页和搜索结果 |
| `seo.default_description` | 默认SEO描述 | "Play the best free online games..." | 显示在搜索结果摘要 |
| `seo.keywords` | 网站关键词 | "online games, free games, arcade games" | 用逗号分隔，影响搜索排名 |
| `seo.language` | 网站语言 | "en" 或 "zh-CN" | 影响搜索引擎索引和本地化 |
| `seo.author` | 网站作者 | "GamePort Team" | 显示在页面元数据中 |
| `contact.email` | 联系邮箱 | "<EMAIL>" | 显示在联系页面和FAQ |
| `contact.response_time` | 回复时间 | "24-48 hours" | 告知用户预期回复时间 |
| `iframe_settings.default_size` | 默认游戏窗口大小 | "small" | small(854x480) 或 large(960x540) |
| `iframe_settings.sizes.small` | 小窗口尺寸 | width: 854, height: 480 | 适合大多数游戏的标准尺寸 |
| `iframe_settings.sizes.large` | 大窗口尺寸 | width: 960, height: 540 | 适合需要更大显示区域的游戏 |
| `display_settings.homepage.new_games_display.count` | 新游戏显示数量 | 8 | 首页"新游戏"区域显示的游戏数量 |
| `display_settings.homepage.new_games_display.selected_games` | 新游戏列表 | ["space-invaders", "frogger", ...] | 要显示的游戏ID数组 |
| `display_settings.homepage.popular_games_display.count` | 热门游戏显示数量 | 6 | 首页"热门游戏"区域显示的游戏数量 |
| `display_settings.homepage.popular_games_display.columns` | 热门游戏列数 | 2 | 热门游戏区域的列数布局 |
| `display_settings.homepage.popular_games_display.selected_games` | 热门游戏列表 | ["super-mario-bros", "pac-man", ...] | 要显示的游戏ID数组 |

### games.json 数据结构详解

#### 首页特殊游戏（数组第一个对象，必须存在）
```json
{
  "id": "homepage-special",                    // 固定ID，不能修改
  "name": "Space Odyssey",                     // 游戏名称，显示在首页大图区域
  "category": "popular",                       // 分类，可以是popular或new
  "thumbnail": "images/common/homepage-game.jpeg", // 缩略图路径
  "gameUrl": "https://wowtbc.net/sprunkin/deluxe+ocs/index.html", // 游戏链接
  "feature_blocks": [                          // 游戏详情页内容块（可选）
    {
      "type": "youtube",                       // YouTube视频块
      "videoId": "some-video-id",              // 视频ID
      "title": "Space Odyssey Gameplay"       // 视频标题
    },
    {
      "type": "section",                       // 文本内容块
      "title": "What is Space Odyssey",       // 内容标题
      "content": "Space Odyssey is an epic space exploration game...", // 内容文本
      "format": "paragraph"                    // 显示格式：paragraph或list
    }
  ],
  "meta": {                                    // SEO元数据（可选）
    "title": "Welcome to GamePort | Play the Latest and Hottest Web Games",
    "description": "GamePort is your ultimate gaming portal, explore massive exciting games."
  }
}
```

#### 普通游戏示例（数组其余对象）
```json
{
  "id": "pac-man",                             // 游戏唯一ID
  "name": "Pac-Man",                           // 游戏显示名称
  "category": "popular",                       // 分类：popular（热门）或new（新游戏）
  "thumbnail": "images/popular_games_image/pac-man.png", // 缩略图路径
  "gameUrl": "/games-source/pac-man/index.html", // 游戏链接（本地路径）
  "feature_blocks": [                          // 游戏详情页内容（可选）
    {
      "type": "youtube",
      "videoId": "dScq4P5gn4A",               // 真实的YouTube视频ID
      "title": "Pac-Man Classic Arcade Gameplay"
    },
    {
      "type": "section",
      "title": "What is Pac-Man",
      "content": "Pac-Man is the iconic arcade game that became a global phenomenon...",
      "format": "paragraph"
    },
    {
      "type": "section",
      "title": "How to Play",
      "content": "Use the arrow keys or WASD to guide Pac-Man through the maze|Eat all the small dots while avoiding the four ghosts|When you eat a large power pellet, the ghosts turn blue and become vulnerable|Chase them down for bonus points",
      "format": "list"                         // 列表格式，用|分隔每个项目
    }
  ],
  "meta": {                                    // SEO元数据（可选）
    "title": "Play Pac-Man Online - Classic Arcade Game | GamePort",
    "description": "Play the original Pac-Man arcade game online. Navigate mazes, eat dots, and avoid ghosts in this timeless classic."
  }
}
```

#### 当前games.json中包含的游戏：
1. **homepage-special** - Space Odyssey（首页特殊游戏）
2. **super-mario-bros** - Super Mario Bros（热门游戏）
3. **pac-man** - Pac-Man（热门游戏）
4. **tetris** - Tetris（热门游戏）
5. **space-invaders** - Space Invaders（新游戏）
6. **frogger** - Frogger（新游戏）
7. **snake** - Snake（新游戏）

## 🎨 主题系统

### 内置主题详解

- `default` - 默认蓝色主题，简洁现代的设计风格，适合大多数游戏网站
- `retro` - 复古风格，怀旧的色彩搭配，适合经典游戏展示
- `cyberpunk` - 赛博朋克风格，霓虹色彩和未来感设计，适合科幻类游戏
- `forest` - 森林主题，绿色自然风格，清新的视觉体验
- `ocean` - 海洋主题，蓝色清新风格，宁静的视觉感受
- `battle` - 战斗主题，红色激烈风格，适合动作类游戏（当前使用）
- `dark` - 暗黑主题，深色护眼设计，适合长时间游戏

### 切换主题

在 `config.json` 中修改 `selected_theme` 字段：
```json
{
  "selected_theme": "battle"  // 当前使用的是battle主题
}
```

**可选主题值：**
- `"default"` - 默认蓝色主题
- `"retro"` - 复古风格主题
- `"cyberpunk"` - 赛博朋克主题
- `"forest"` - 森林绿色主题
- `"ocean"` - 海洋蓝色主题
- `"battle"` - 战斗红色主题（当前）
- `"dark"` - 暗黑主题

修改后重新运行构建命令：
```bash
npm run build
```

### 自定义主题

如果内置的7个主题不满足需求，可以创建自定义主题：

1. **创建主题文件**：在 `src/css/themes/` 目录下创建新的主题文件：`theme-mytheme.css`
2. **添加到配置**：在 `config.json` 的 `available_themes` 数组中添加 `"mytheme"`
3. **应用主题**：设置 `selected_theme` 为 `"mytheme"`
4. **重新构建**：运行 `npm run build` 应用新主题

**主题文件示例**：
```css
/* src/css/themes/theme-mytheme.css */
:root {
  --primary-color: #your-color;
  --secondary-color: #your-color;
  --background-color: #your-color;
  /* 更多CSS变量... */
}
```

## 🔧 高级功能

### Feature Blocks 内容块

支持多种类型的内容块：

#### YouTube视频块
```json
{
  "type": "youtube",
  "videoId": "rLl9XBg7wSs",                   // 从YouTube URL提取的视频ID
  "title": "Super Mario Bros Gameplay"        // 视频标题，显示在视频上方
}
```
**说明**：从YouTube URL `https://www.youtube.com/watch?v=rLl9XBg7wSs` 中提取 `rLl9XBg7wSs` 作为videoId

#### 文本内容块 - 段落格式
```json
{
  "type": "section",
  "title": "What is Super Mario Bros",         // 内容块标题
  "content": "Super Mario Bros is a legendary platform adventure game that revolutionized the gaming industry. It follows Mario on his quest to rescue Princess Peach from the evil Bowser.", // 段落内容
  "format": "paragraph"                        // 段落格式
}
```

#### 文本内容块 - 列表格式
```json
{
  "type": "section",
  "title": "How to Play",                      // 内容块标题
  "content": "Use the arrow keys or WASD to move Mario left and right|Press the spacebar or up arrow to jump|Your goal is to reach the flagpole at the end of each level|Collect coins and power-ups along the way", // 用|分隔的列表项
  "format": "list"                             // 列表格式，会生成有序列表
}
```

### SEO优化

- 每个游戏页面自动生成独立的SEO元数据
- 支持Open Graph和Twitter Card
- 自动生成sitemap（可扩展）
- 语义化HTML结构

### 响应式设计

- 移动优先设计
- 支持手机、平板、桌面设备
- 自适应游戏卡片布局
- 触摸友好的交互设计

## 📦 部署

### 推荐部署平台

本模板支持源码直接部署，无需本地构建。推荐使用以下平台：

#### 🔥 Cloudflare Pages（推荐）

**优势**: 免费、快速、全球CDN、自动HTTPS

**部署步骤**:
1. 将项目代码推送到GitHub/GitLab仓库
2. 登录 [Cloudflare Pages](https://pages.cloudflare.com/)
3. 点击"创建项目" → "连接到Git"
4. 选择你的仓库
5. 配置构建设置：
   - **构建命令**: `npm run build`
   - **构建输出目录**: `dist`
   - **Node.js版本**: `18` 或更高
6. 点击"保存并部署"

**自动部署**: 每次推送代码到仓库，Cloudflare Pages会自动重新构建和部署。

#### ⚡ Vercel（推荐）

**优势**: 零配置、自动优化、边缘网络

**部署步骤**:
1. 将项目代码推送到GitHub/GitLab/Bitbucket仓库
2. 登录 [Vercel](https://vercel.com/)
3. 点击"New Project"
4. 导入你的Git仓库
5. Vercel会自动检测到这是Node.js项目并配置：
   - **构建命令**: `npm run build`
   - **输出目录**: `dist`
6. 点击"Deploy"

**自动部署**: 每次推送到主分支会自动触发重新部署。

### 本地构建部署（备选方案）

如果需要手动部署到其他平台：

```bash
# 本地构建
npm run build

# 将 dist/ 目录内容上传到你的Web服务器
```

## 🛠️ 开发指南

### 修改模板

- 主页模板：`src/templates/_main-layout-template.html`
- 游戏详情页：`src/templates/game-detail-template.html`
- 列表页模板：`src/templates/_list-layout-template.html`

### 添加新组件

1. 在 `src/components/` 创建组件HTML文件
2. 在 `build.js` 中添加组件处理逻辑
3. 在模板中使用占位符引用组件

### 自定义样式

- 主样式：`src/css/style.css`
- 主题样式：`src/css/themes/theme-*.css`
- 使用CSS变量系统便于主题切换

### 图片资源管理

**图片目录结构说明：**

1. **公共图片目录** (`src/images/common/`)：
   - `logo.png` - 网站Logo，显示在所有页面的导航栏
   - `favicon.ico` - 网站图标，显示在浏览器标签页
   - `homepage-game.jpeg` - 首页特殊游戏的大图

2. **热门游戏图片目录** (`src/images/popular_games_image/`)：
   - 存放category为"popular"的游戏缩略图
   - 文件名必须与games.json中的游戏ID对应
   - 例如：`super-mario-bros.png`、`pac-man.png`、`tetris.png`

3. **新游戏图片目录** (`src/images/new_games_image/`)：
   - 存放category为"new"的游戏缩略图
   - 文件名必须与games.json中的游戏ID对应
   - 例如：`space-invaders.png`、`frogger.png`、`snake.png`

**添加新游戏图片的步骤：**
1. 准备游戏缩略图（建议300x200px，PNG或JPEG格式）
2. 根据游戏的category，将图片放入对应目录
3. 文件名必须与games.json中的游戏ID完全一致
4. 在games.json中正确设置thumbnail路径

**图片路径示例：**
```json
{
  "id": "new-game",
  "category": "new",
  "thumbnail": "images/new_games_image/new-game.png"  // 路径必须正确
}
```

## 📋 构建脚本

### 可用命令

```bash
npm run build    # 构建生产版本
npm run dev      # 开发模式构建
npm run clean    # 清理输出目录
```

### 构建过程详解

构建脚本 `build.js` 执行以下步骤：

1. **读取配置文件**：
   - 加载 `config.json` 获取网站配置
   - 加载 `games.json` 获取游戏数据

2. **处理模板文件**：
   - 读取 `src/templates/` 下的HTML模板
   - 替换模板中的占位符（如 `{{SITE_NAME}}`、`{{GAMES_LIST}}` 等）
   - 根据配置生成动态内容

3. **生成游戏内容**：
   - 为每个游戏生成独立的详情页
   - 生成游戏卡片组件
   - 处理feature_blocks内容块

4. **处理静态资源**：
   - 复制CSS文件到输出目录
   - 应用选定的主题样式
   - 复制JavaScript文件
   - 复制所有图片资源（保持目录结构）

5. **输出构建结果**：
   - 生成的文件输出到 `dist/` 目录
   - 创建完整的静态网站结构
   - 生成SEO友好的HTML文件

**构建后的dist目录结构：**
```
dist/
├── index.html                    # 主页
├── popular_games/               # 热门游戏详情页目录
│   ├── super-mario-bros.html
│   ├── pac-man.html
│   └── tetris.html
├── new_games/                   # 新游戏详情页目录
│   ├── space-invaders.html
│   ├── frogger.html
│   └── snake.html
├── css/                         # 样式文件
│   ├── style.css
│   └── themes/
├── js/                          # JavaScript文件
│   └── main.js
├── images/                      # 图片资源（完整复制）
│   ├── common/
│   │   ├── logo.png
│   │   ├── favicon.ico
│   │   └── homepage-game.jpeg
│   ├── popular_games_image/
│   └── new_games_image/
└── legal_info/                  # 法律信息页面
    ├── About-Us.html
    ├── Contact-Us.html
    ├── DMCA.html
    └── Privacy-Policy.html
```

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个模板！

## 📄 许可证

MIT License - 详见 LICENSE 文件

## 🆘 支持和帮助

### 常见问题

**Q: 如何添加新游戏？**
A: 在 `games.json` 中添加新的游戏对象，确保ID唯一，然后在 `config.json` 的 `display_settings` 中配置显示位置。

**Q: 游戏无法加载怎么办？**
A: 检查 `gameUrl` 是否正确，确保外部链接可访问或本地文件存在。

**Q: 如何修改首页显示的游戏？**
A: 修改 `config.json` 中的 `display_settings.homepage.new_games_display.selected_games` 和 `popular_games_display.selected_games` 数组。

**Q: 主题切换后没有效果？**
A: 确保主题名称在 `available_themes` 中存在，修改后需要重新运行 `npm run build`。

**Q: 图片无法显示怎么办？**
A: 检查图片文件是否存在于正确的目录中，确保文件名与games.json中的路径完全一致，注意大小写。

**Q: Logo或Favicon不显示？**
A: 确保 `src/images/common/logo.png` 和 `src/images/common/favicon.ico` 文件存在，Logo建议使用透明背景的PNG格式。

**Q: 如何替换网站Logo？**
A: 将新的Logo文件命名为 `logo.png`，放入 `src/images/common/` 目录，建议尺寸200x50px，透明背景。

### 获取帮助

如有问题，请查看：
- 项目文档：`PRD.md` 和 `BUILD_PLAN.md`
- 配置文件示例：当前的 `config.json` 和 `games.json`
- 提交Issue获取技术支持
- 联系邮箱：<EMAIL>（24-48小时内回复）

### 技术支持

- **配置问题**：检查JSON文件格式是否正确
- **构建问题**：确保Node.js版本 >= 14.0.0
- **部署问题**：查看部署平台的构建日志
- **样式问题**：检查主题文件是否存在
